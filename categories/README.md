# ArXiv论文分类项目

## 项目描述
这是一个用于处理ArXiv学术论文数据的Java项目，主要功能是根据论文的主要分类（arxiv_primary_category）对论文进行分类，并生成JSON格式的结果文件。

## 项目结构
```
categories/
├── src/main/java/org/example/
│   └── Main.java                 # 主程序文件
├── pom.xml                       # Maven配置文件
├── categories_result.json        # 输出结果文件
└── README.md                     # 项目说明文件
```

## 功能特性
- 读取ArXiv论文数据集（JSONL格式）
- 按照arxiv_primary_category字段对论文进行分类
- 生成包含每个分类及其对应论文ID列表的JSON文件
- 提供详细的统计信息

## 数据处理结果
- **总论文数**: 200,031篇
- **总分类数**: 149个
- **输出格式**: JSON数组，每个元素包含category和id字段

### 论文数量最多的前10个分类：
1. cs.CV (计算机视觉): 17,490篇论文
2. cs.LG (机器学习): 15,226篇论文
3. cs.CL (计算语言学): 9,495篇论文
4. quant-ph (量子物理): 8,447篇论文
5. hep-ph (高能物理-现象学): 4,974篇论文
6. cs.RO (机器人学): 4,691篇论文
7. cond-mat.mtrl-sci (凝聚态物理-材料科学): 4,356篇论文
8. math.AP (数学-分析): 4,018篇论文
9. astro-ph.GA (天体物理-星系): 3,914篇论文
10. hep-th (高能物理-理论): 3,804篇论文

## 如何运行

### 前提条件
- Java 11或更高版本
- Maven 3.6或更高版本
- 数据文件位于 `../expdata/` 目录下

### 编译和运行
```bash
# 编译项目
mvn clean compile

# 打包项目
mvn package

# 运行程序
java -jar target/categories-1.0-SNAPSHOT.jar

# 或者指定输入和输出路径
java -jar target/categories-1.0-SNAPSHOT.jar [输入目录] [输出文件]
```

## 输出文件格式
生成的JSON文件格式如下：
```json
[
  {
    "category": "astro-ph.CO",
    "id": [
      "http://arxiv.org/abs/2302.11938v1",
      "http://arxiv.org/abs/2302.12277v3",
      ...
    ]
  },
  {
    "category": "astro-ph.EP", 
    "id": [
      "http://arxiv.org/abs/2302.11939v1",
      ...
    ]
  }
]
```

## 技术实现
- 使用Jackson库进行JSON处理
- 采用HashMap存储分类映射关系
- 支持大规模数据处理（20万条记录）
- 内存高效的流式处理方式

## 依赖项
- Jackson Databind 2.15.2 (JSON处理)
- SLF4J Simple 1.7.36 (日志)

## 作者
实验一：使用关键词对文章分类项目
