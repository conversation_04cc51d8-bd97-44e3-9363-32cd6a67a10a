#!/bin/bash

# ArXiv Category Classification MapReduce Runner Script

echo "=== ArXiv Category Classification MapReduce ==="

# Set paths
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INPUT_DIR="../expdata"
OUTPUT_DIR="output"
FINAL_OUTPUT="categories_result.json"

# Clean previous output
echo "Cleaning previous output..."
rm -rf "$OUTPUT_DIR"
rm -f "$FINAL_OUTPUT"

# Build the project
echo "Building the project..."
cd "$PROJECT_DIR"
mvn clean package -q

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build successful!"

# Run MapReduce job
echo "Running MapReduce job..."
java -cp "target/categories-1.0-SNAPSHOT.jar" org.example.CategoryMapReduce "$INPUT_DIR" "$OUTPUT_DIR"

if [ $? -ne 0 ]; then
    echo "MapReduce job failed!"
    exit 1
fi

echo "MapReduce job completed!"

# Format the output to final JSON
echo "Formatting output to JSON..."
java -cp "target/categories-1.0-SNAPSHOT.jar" org.example.JsonFormatter "$OUTPUT_DIR/part-r-00000" "$FINAL_OUTPUT"

if [ $? -ne 0 ]; then
    echo "JSON formatting failed!"
    exit 1
fi

echo "=== Process completed successfully! ==="
echo "Final output file: $FINAL_OUTPUT"
echo ""
echo "Sample of the results:"
head -20 "$FINAL_OUTPUT"
echo ""
echo "Total lines in output: $(wc -l < "$FINAL_OUTPUT")"
