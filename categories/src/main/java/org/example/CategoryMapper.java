package org.example;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;

/**
 * Hadoop Mapper类 - 提取ArXiv论文的分类和ID
 */
public class CategoryMapper extends Mapper<LongWritable, Text, Text, Text> {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Text categoryKey = new Text();
    private final Text paperIdValue = new Text();
    
    @Override
    protected void map(LongWritable key, Text value, Context context) 
            throws IOException, InterruptedException {
        
        try {
            // 获取输入行并去除空白
            String jsonLine = value.toString().trim();
            if (jsonLine.isEmpty()) {
                return;
            }
            
            // 解析JSON行
            JsonNode jsonNode = objectMapper.readTree(jsonLine);
            
            // 提取arxiv_primary_category和id字段
            JsonNode categoryNode = jsonNode.get("arxiv_primary_category");
            JsonNode idNode = jsonNode.get("id");
            
            if (categoryNode != null && idNode != null) {
                String category = categoryNode.asText();
                String paperId = idNode.asText();
                
                // 检查字段是否为空
                if (!category.isEmpty() && !paperId.isEmpty()) {
                    categoryKey.set(category);
                    paperIdValue.set(paperId);
                    
                    // 输出 (分类, 论文ID) 键值对
                    context.write(categoryKey, paperIdValue);
                    
                    // 输出处理进度（每1000条记录输出一次）
                    if (key.get() % 1000 == 0) {
                        context.setStatus("已处理 " + key.get() + " 行数据");
                    }
                }
            }
            
        } catch (Exception e) {
            // 记录错误但继续处理
            System.err.println("处理行时出错 [行号: " + key.get() + "]: " + e.getMessage());
            System.err.println("问题行内容: " + value.toString());
            context.getCounter("ARXIV_PROCESSING", "PARSE_ERRORS").increment(1);
        }
    }
}
