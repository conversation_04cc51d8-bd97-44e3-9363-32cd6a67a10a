package org.example;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;

/**
 * Hadoop Mapper类 - 提取ArXiv论文的分类和ID
 */
public class CategoryMapper extends Mapper<LongWritable, Text, Text, Text> {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Text categoryKey = new Text();
    private final Text paperIdValue = new Text();

    @Override
    protected void map(LongWritable key, Text value, Context context) throws IOException, InterruptedException {

        try {
            String jsonLine = value.toString().trim();
            if (jsonLine.isEmpty()) {
                return;
            }

            JsonNode jsonNode = objectMapper.readTree(jsonLine);
            JsonNode categoryNode = jsonNode.get("arxiv_primary_category");
            JsonNode idNode = jsonNode.get("id");

            if (categoryNode != null && idNode != null) {
                String category = categoryNode.asText();
                String paperId = idNode.asText();

                if (!category.isEmpty() && !paperId.isEmpty()) {
                    categoryKey.set(category);
                    paperIdValue.set(paperId);
                    context.write(categoryKey, paperIdValue);
                }
            }

        } catch (Exception e) {
            // 忽略解析错误，继续处理
        }
    }
}
