package org.example;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;

/**
 * Mapper class for extracting arxiv_primary_category and paper ID from JSON data
 */
public class CategoryMapper extends Mapper<LongWritable, Text, Text, Text> {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Text categoryKey = new Text();
    private final Text paperIdValue = new Text();
    
    @Override
    protected void map(LongWritable key, Text value, Context context) 
            throws IOException, InterruptedException {
        
        try {
            // Parse JSON line
            String jsonLine = value.toString().trim();
            if (jsonLine.isEmpty()) {
                return;
            }
            
            JsonNode jsonNode = objectMapper.readTree(jsonLine);
            
            // Extract arxiv_primary_category and id
            JsonNode categoryNode = jsonNode.get("arxiv_primary_category");
            JsonNode idNode = jsonNode.get("id");
            
            if (categoryNode != null && idNode != null) {
                String category = categoryNode.asText();
                String paperId = idNode.asText();
                
                // Skip empty values
                if (!category.isEmpty() && !paperId.isEmpty()) {
                    categoryKey.set(category);
                    paperIdValue.set(paperId);
                    
                    // Emit category as key and paper ID as value
                    context.write(categoryKey, paperIdValue);
                }
            }
            
        } catch (Exception e) {
            // Log error but continue processing
            System.err.println("Error processing line: " + value.toString());
            System.err.println("Error: " + e.getMessage());
        }
    }
}
