package org.example;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;

/**
 * MapReduce driver for categorizing arXiv papers by primary category
 */
public class CategoryMapReduce {
    
    public static void main(String[] args) throws Exception {
        if (args.length != 2) {
            System.err.println("Usage: CategoryMapReduce <input path> <output path>");
            System.err.println("Example: CategoryMapReduce /path/to/input /path/to/output");
            System.exit(-1);
        }

        Configuration conf = new Configuration();
        
        // Configure for local mode (standalone)
        conf.set("fs.defaultFS", "file:///");
        conf.set("mapreduce.framework.name", "local");
        conf.set("mapreduce.jobtracker.address", "local");
        
        // Set up the job
        Job job = Job.getInstance(conf, "arxiv-category-classification");
        job.setJarByClass(CategoryMapReduce.class);
        
        // Set mapper and reducer classes
        job.setMapperClass(CategoryMapper.class);
        job.setReducerClass(CategoryReducer.class);
        
        // Set output key and value types
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);
        
        // Set input and output formats
        job.setInputFormatClass(TextInputFormat.class);
        job.setOutputFormatClass(TextOutputFormat.class);
        
        // Set input and output paths
        FileInputFormat.addInputPath(job, new Path(args[0]));
        FileOutputFormat.setOutputPath(job, new Path(args[1]));
        
        // Print job information
        System.out.println("Starting ArXiv Category Classification MapReduce Job");
        System.out.println("Input path: " + args[0]);
        System.out.println("Output path: " + args[1]);
        
        // Wait for the job to complete
        boolean success = job.waitForCompletion(true);
        
        if (success) {
            System.out.println("Job completed successfully!");
        } else {
            System.err.println("Job failed!");
        }
        
        System.exit(success ? 0 : 1);
    }
}
