package org.example;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * Utility class to format MapReduce output into the required JSON format
 */
public class JsonFormatter {
    
    public static void main(String[] args) throws Exception {
        if (args.length != 2) {
            System.err.println("Usage: JsonFormatter <input_file> <output_file>");
            System.exit(-1);
        }
        
        String inputFile = args[0];
        String outputFile = args[1];
        
        ObjectMapper objectMapper = new ObjectMapper();
        List<JsonNode> categories = new ArrayList<>();
        
        // Read the MapReduce output file
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(inputFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    try {
                        // Parse each JSON object from the MapReduce output
                        JsonNode categoryNode = objectMapper.readTree(line);
                        categories.add(categoryNode);
                    } catch (Exception e) {
                        System.err.println("Error parsing line: " + line);
                        System.err.println("Error: " + e.getMessage());
                    }
                }
            }
        }
        
        // Create the final JSON array
        ArrayNode finalArray = objectMapper.createArrayNode();
        for (JsonNode category : categories) {
            finalArray.add(category);
        }
        
        // Write the formatted JSON to output file
        try (FileWriter writer = new FileWriter(outputFile)) {
            String prettyJson = objectMapper.writerWithDefaultPrettyPrinter()
                    .writeValueAsString(finalArray);
            writer.write(prettyJson);
        }
        
        System.out.println("JSON formatting completed!");
        System.out.println("Output written to: " + outputFile);
        System.out.println("Total categories found: " + categories.size());
    }
}
