package org.example;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 将MapReduce输出转换为JSON数组格式
 */
public class JsonFormatter {
    
    public static void main(String[] args) throws Exception {
        String inputFile = "hadoop_output/part-r-00000";
        String outputFile = "categories_result.json";
        
        ObjectMapper objectMapper = new ObjectMapper();
        List<JsonNode> categories = new ArrayList<>();
        
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(inputFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    JsonNode categoryNode = objectMapper.readTree(line);
                    categories.add(categoryNode);
                }
            }
        }
        
        categories.sort((a, b) -> {
            String categoryA = a.get("category").asText();
            String categoryB = b.get("category").asText();
            return categoryA.compareTo(categoryB);
        });
        
        ArrayNode finalArray = objectMapper.createArrayNode();
        for (JsonNode category : categories) {
            finalArray.add(category);
        }
        
        try (FileWriter writer = new FileWriter(outputFile)) {
            String prettyJson = objectMapper.writerWithDefaultPrettyPrinter()
                    .writeValueAsString(finalArray);
            writer.write(prettyJson);
        }
        
        System.out.println("转换完成: " + outputFile);
        System.out.println("总分类数: " + categories.size());
    }
}
