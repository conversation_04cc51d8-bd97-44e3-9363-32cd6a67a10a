package org.example;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 后处理工具 - 将MapReduce输出转换为最终JSON格式
 */
public class JsonFormatter {
    
    public static void main(String[] args) throws Exception {
        if (args.length != 2) {
            System.err.println("用法: java -cp target/classes org.example.JsonFormatter <MapReduce输出文件> <最终JSON文件>");
            System.err.println("示例: java -cp target/classes org.example.JsonFormatter output/part-r-00000 categories_result.json");
            System.exit(-1);
        }
        
        String inputFile = args[0];
        String outputFile = args[1];
        
        System.out.println("=== JSON格式化工具 ===");
        System.out.println("输入文件: " + inputFile);
        System.out.println("输出文件: " + outputFile);
        
        ObjectMapper objectMapper = new ObjectMapper();
        List<JsonNode> categories = new ArrayList<>();
        
        // 读取MapReduce输出文件
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(inputFile))) {
            String line;
            int lineCount = 0;
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    try {
                        // 每行格式为: \t{json}，需要去掉开头的制表符
                        if (line.startsWith("\t")) {
                            line = line.substring(1);
                        }
                        
                        // 解析JSON对象
                        JsonNode categoryNode = objectMapper.readTree(line);
                        categories.add(categoryNode);
                        lineCount++;
                        
                        if (lineCount % 10 == 0) {
                            System.out.println("已处理 " + lineCount + " 个分类");
                        }
                        
                    } catch (Exception e) {
                        System.err.println("解析行时出错: " + line);
                        System.err.println("错误: " + e.getMessage());
                    }
                }
            }
            
            System.out.println("总共读取了 " + lineCount + " 个分类");
        }
        
        // 按分类名称排序
        categories.sort((a, b) -> {
            String categoryA = a.get("category").asText();
            String categoryB = b.get("category").asText();
            return categoryA.compareTo(categoryB);
        });
        
        // 创建最终的JSON数组
        ArrayNode finalArray = objectMapper.createArrayNode();
        for (JsonNode category : categories) {
            finalArray.add(category);
        }
        
        // 写入格式化的JSON文件
        try (FileWriter writer = new FileWriter(outputFile)) {
            String prettyJson = objectMapper.writerWithDefaultPrettyPrinter()
                    .writeValueAsString(finalArray);
            writer.write(prettyJson);
        }
        
        System.out.println("\n=== 格式化完成! ===");
        System.out.println("输出文件: " + outputFile);
        System.out.println("总分类数: " + categories.size());
        
        // 显示统计信息
        showStatistics(categories, objectMapper);
    }
    
    /**
     * 显示统计信息
     */
    private static void showStatistics(List<JsonNode> categories, ObjectMapper objectMapper) {
        System.out.println("\n=== 分类统计信息 ===");
        
        // 计算总论文数和按论文数排序
        List<CategoryStat> stats = new ArrayList<>();
        int totalPapers = 0;
        
        for (JsonNode category : categories) {
            String categoryName = category.get("category").asText();
            int paperCount = category.get("id").size();
            totalPapers += paperCount;
            stats.add(new CategoryStat(categoryName, paperCount));
        }
        
        // 按论文数量降序排序
        stats.sort((a, b) -> Integer.compare(b.paperCount, a.paperCount));
        
        System.out.println("论文数量最多的前10个分类:");
        for (int i = 0; i < Math.min(10, stats.size()); i++) {
            CategoryStat stat = stats.get(i);
            System.out.printf("%2d. %-25s: %d篇论文\n", 
                i + 1, stat.categoryName, stat.paperCount);
        }
        
        System.out.println("\n总体统计:");
        System.out.println("总分类数: " + categories.size());
        System.out.println("总论文数: " + totalPapers);
        System.out.printf("平均每个分类论文数: %.2f\n", (double) totalPapers / categories.size());
    }
    
    /**
     * 分类统计辅助类
     */
    private static class CategoryStat {
        String categoryName;
        int paperCount;
        
        CategoryStat(String categoryName, int paperCount) {
            this.categoryName = categoryName;
            this.paperCount = paperCount;
        }
    }
}
