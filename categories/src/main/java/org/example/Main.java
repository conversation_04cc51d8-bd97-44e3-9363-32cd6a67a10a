package org.example;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

public class Main {

    public static void main(String[] args) throws Exception {
        String inputDir = "../expdata";
        String outputFile = "categories_result.json";

        if (args.length >= 1) {
            inputDir = args[0];
        }
        if (args.length >= 2) {
            outputFile = args[1];
        }

        System.out.println("=== ArXiv论文分类程序 ===");
        System.out.println("输入目录: " + inputDir);
        System.out.println("输出文件: " + outputFile);

        // 存储每个分类对应的论文ID列表
        Map<String, List<String>> categoryMap = new HashMap<>();
        ObjectMapper objectMapper = new ObjectMapper();

        // 获取输入目录下的所有.jsonl文件
        File inputDirectory = new File(inputDir);
        if (!inputDirectory.exists() || !inputDirectory.isDirectory()) {
            System.err.println("错误: 输入目录不存在: " + inputDir);
            System.exit(1);
        }

        File[] jsonlFiles = inputDirectory.listFiles((dir, name) -> name.endsWith(".jsonl"));
        if (jsonlFiles == null || jsonlFiles.length == 0) {
            System.err.println("错误: 在目录中没有找到.jsonl文件: " + inputDir);
            System.exit(1);
        }

        System.out.println("找到 " + jsonlFiles.length + " 个数据文件");

        int totalPapers = 0;
        int processedPapers = 0;

        // 处理每个文件
        for (File file : jsonlFiles) {
            System.out.println("正在处理文件: " + file.getName());

            try (BufferedReader reader = Files.newBufferedReader(file.toPath())) {
                String line;
                while ((line = reader.readLine()) != null) {
                    totalPapers++;
                    line = line.trim();
                    if (line.isEmpty())
                        continue;

                    try {
                        // 解析JSON
                        JsonNode jsonNode = objectMapper.readTree(line);

                        // 提取分类和ID
                        JsonNode categoryNode = jsonNode.get("arxiv_primary_category");
                        JsonNode idNode = jsonNode.get("id");

                        if (categoryNode != null && idNode != null) {
                            String category = categoryNode.asText();
                            String paperId = idNode.asText();

                            if (!category.isEmpty() && !paperId.isEmpty()) {
                                // 添加到分类映射中
                                categoryMap.computeIfAbsent(category, k -> new ArrayList<>()).add(paperId);
                                processedPapers++;
                            }
                        }

                    } catch (Exception e) {
                        System.err.println("解析JSON行时出错: " + e.getMessage());
                    }
                }
            } catch (IOException e) {
                System.err.println("读取文件时出错: " + file.getName() + " - " + e.getMessage());
            }
        }

        System.out.println("处理完成!");
        System.out.println("总论文数: " + totalPapers);
        System.out.println("成功处理: " + processedPapers);
        System.out.println("发现分类数: " + categoryMap.size());

        // 生成最终的JSON输出
        generateJsonOutput(categoryMap, outputFile, objectMapper);

        // 显示统计信息
        showStatistics(categoryMap);
    }

    /**
     * 生成JSON输出文件
     */
    private static void generateJsonOutput(Map<String, List<String>> categoryMap,
            String outputFile, ObjectMapper objectMapper) {
        try {
            // 创建JSON数组
            ArrayNode resultArray = objectMapper.createArrayNode();

            // 按分类名称排序
            List<String> sortedCategories = new ArrayList<>(categoryMap.keySet());
            Collections.sort(sortedCategories);

            for (String category : sortedCategories) {
                ObjectNode categoryObject = objectMapper.createObjectNode();
                categoryObject.put("category", category);

                // 创建ID数组
                ArrayNode idsArray = objectMapper.createArrayNode();
                List<String> paperIds = categoryMap.get(category);
                for (String paperId : paperIds) {
                    idsArray.add(paperId);
                }
                categoryObject.set("id", idsArray);

                resultArray.add(categoryObject);
            }

            // 写入文件
            try (FileWriter writer = new FileWriter(outputFile)) {
                String prettyJson = objectMapper.writerWithDefaultPrettyPrinter()
                        .writeValueAsString(resultArray);
                writer.write(prettyJson);
            }

            System.out.println("JSON文件生成成功: " + outputFile);

        } catch (IOException e) {
            System.err.println("生成JSON文件时出错: " + e.getMessage());
        }
    }

    /**
     * 显示统计信息
     */
    private static void showStatistics(Map<String, List<String>> categoryMap) {
        System.out.println("\n=== 分类统计信息 ===");

        // 按论文数量排序显示前10个分类
        List<Map.Entry<String, List<String>>> sortedEntries = new ArrayList<>(categoryMap.entrySet());
        sortedEntries.sort((a, b) -> Integer.compare(b.getValue().size(), a.getValue().size()));

        System.out.println("论文数量最多的前10个分类:");
        for (int i = 0; i < Math.min(10, sortedEntries.size()); i++) {
            Map.Entry<String, List<String>> entry = sortedEntries.get(i);
            System.out.printf("%2d. %-25s: %d篇论文\n",
                    i + 1, entry.getKey(), entry.getValue().size());
        }

        // 总体统计
        int totalPapers = categoryMap.values().stream().mapToInt(List::size).sum();
        System.out.println("\n总体统计:");
        System.out.println("总分类数: " + categoryMap.size());
        System.out.println("总论文数: " + totalPapers);
        System.out.printf("平均每个分类论文数: %.2f\n", (double) totalPapers / categoryMap.size());
    }
}