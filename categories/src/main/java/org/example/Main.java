package org.example;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;

/**
 * Main class for running the Category MapReduce job
 */
public class Main {
    public static void main(String[] args) throws Exception {
        if (args.length != 2) {
            System.err.println("Usage: CategoryMapReduce <input path> <output path>");
            System.exit(-1);
        }

        Configuration conf = new Configuration();

        // Set up the job
        Job job = Job.getInstance(conf, "arxiv category classification");
        job.setJarByClass(Main.class);

        // Set mapper and reducer classes
        job.setMapperClass(CategoryMapper.class);
        job.setReducerClass(CategoryReducer.class);

        // Set output key and value types
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // Set input and output formats
        job.setInputFormatClass(TextInputFormat.class);
        job.setOutputFormatClass(TextOutputFormat.class);

        // Set input and output paths
        FileInputFormat.addInputPath(job, new Path(args[0]));
        FileOutputFormat.setOutputPath(job, new Path(args[1]));

        // Wait for the job to complete
        boolean success = job.waitForCompletion(true);
        System.exit(success ? 0 : 1);
    }
}