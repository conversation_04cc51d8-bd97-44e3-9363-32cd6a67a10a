package org.example;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;

/**
 * Hadoop MapReduce主程序 - ArXiv论文分类
 */
public class Main {

    public static void main(String[] args) throws Exception {
        // Hadoop jar命令会将主类名作为第一个参数，需要跳过
        String[] actualArgs = args;
        if (args.length > 0 && args[0].equals("org.example.Main")) {
            actualArgs = new String[args.length - 1];
            System.arraycopy(args, 1, actualArgs, 0, args.length - 1);
        }

        Configuration conf = new Configuration();
        conf.set("fs.defaultFS", "file:///");
        conf.set("mapreduce.framework.name", "local");

        Job job = Job.getInstance(conf, "arxiv-category-classification");
        job.setJarByClass(Main.class);
        job.setMapperClass(CategoryMapper.class);
        job.setReducerClass(CategoryReducer.class);
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);
        job.setInputFormatClass(TextInputFormat.class);
        job.setOutputFormatClass(TextOutputFormat.class);
        job.setNumReduceTasks(1);

        FileInputFormat.addInputPath(job, new Path(actualArgs[0]));
        FileOutputFormat.setOutputPath(job, new Path(actualArgs[1]));

        System.exit(job.waitForCompletion(true) ? 0 : 1);
    }
}
