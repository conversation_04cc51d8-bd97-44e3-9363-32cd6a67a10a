package org.example;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.hadoop.mapreduce.Counters;
import org.apache.hadoop.mapreduce.Counter;

/**
 * Hadoop MapReduce主程序 - ArXiv论文分类
 */
public class Main {

    public static void main(String[] args) throws Exception {
        if (args.length != 2) {
            System.err.println("用法: hadoop jar categories.jar org.example.Main <输入路径> <输出路径>");
            System.err.println("示例: hadoop jar categories.jar org.example.Main ../expdata output");
            System.exit(-1);
        }

        Configuration conf = new Configuration();

        // 强制使用本地模式运行
        conf.set("fs.defaultFS", "file:///");
        conf.set("mapreduce.framework.name", "local");
        conf.set("mapreduce.jobtracker.address", "local");
        conf.set("yarn.resourcemanager.address", "local");

        // 禁用一些可能导致问题的功能
        conf.set("mapreduce.app-submission.cross-platform", "true");
        conf.set("mapreduce.job.user.classpath.first", "true");

        // 设置一些优化参数
        conf.set("mapreduce.map.memory.mb", "512");
        conf.set("mapreduce.reduce.memory.mb", "512");
        conf.set("mapreduce.task.io.sort.mb", "128");

        Job job = Job.getInstance(conf, "arxiv-category-classification");
        job.setJarByClass(Main.class);

        // 设置Mapper和Reducer类
        job.setMapperClass(CategoryMapper.class);
        job.setReducerClass(CategoryReducer.class);

        // 设置输出键值类型
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // 设置输入输出格式
        job.setInputFormatClass(TextInputFormat.class);
        job.setOutputFormatClass(TextOutputFormat.class);

        // 设置Reducer数量（单个Reducer确保所有结果在一个文件中）
        job.setNumReduceTasks(1);

        // 设置输入输出路径
        FileInputFormat.addInputPath(job, new Path(args[0]));
        FileOutputFormat.setOutputPath(job, new Path(args[1]));

        System.out.println("=== Hadoop MapReduce ArXiv论文分类任务 ===");
        System.out.println("输入路径: " + args[0]);
        System.out.println("输出路径: " + args[1]);
        System.out.println("Mapper类: " + CategoryMapper.class.getName());
        System.out.println("Reducer类: " + CategoryReducer.class.getName());
        System.out.println("开始执行MapReduce任务...");

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 等待任务完成
        boolean success = job.waitForCompletion(true);

        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long duration = (endTime - startTime) / 1000;

        if (success) {
            System.out.println("\n=== MapReduce任务执行成功! ===");
            System.out.println("执行时间: " + duration + " 秒");

            // 显示计数器信息
            Counters counters = job.getCounters();
            if (counters != null) {
                Counter categoriesProcessed = counters.findCounter("ARXIV_PROCESSING", "CATEGORIES_PROCESSED");
                Counter totalPapers = counters.findCounter("ARXIV_PROCESSING", "TOTAL_PAPERS");
                Counter parseErrors = counters.findCounter("ARXIV_PROCESSING", "PARSE_ERRORS");
                Counter reduceErrors = counters.findCounter("ARXIV_PROCESSING", "REDUCE_ERRORS");

                System.out.println("\n=== 处理统计 ===");
                System.out.println("处理的分类数: " + categoriesProcessed.getValue());
                System.out.println("处理的论文总数: " + totalPapers.getValue());
                System.out.println("解析错误数: " + parseErrors.getValue());
                System.out.println("Reduce错误数: " + reduceErrors.getValue());
            }

            System.out.println("\n输出文件位置: " + args[1] + "/part-r-00000");
            System.out.println("请使用以下命令查看结果:");
            System.out.println("cat " + args[1] + "/part-r-00000");

        } else {
            System.err.println("MapReduce任务执行失败!");
        }

        System.exit(success ? 0 : 1);
    }
}
