package org.example;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;

/**
 * Hadoop MapReduce主程序 - ArXiv论文分类
 */
public class Main {

    public static void main(String[] args) throws Exception {
        // 设置默认参数（用于IDE直接运行）
        String inputPath = "./expdata";
        String outputPath = "hadoop_output";

        // 处理命令行参数
        if (args.length >= 2) {
            // Hadoop jar命令会将主类名作为第一个参数，需要跳过
            if (args[0].equals("org.example.Main")) {
                inputPath = args[1];
                outputPath = args[2];
            } else {
                inputPath = args[0];
                outputPath = args[1];
            }
        }

        // 转换为绝对路径以避免Hadoop路径解析问题
        java.io.File inputFile = new java.io.File(inputPath);
        java.io.File outputFile = new java.io.File(outputPath);
        inputPath = inputFile.getAbsolutePath();
        outputPath = outputFile.getAbsolutePath();

        Configuration conf = new Configuration();
        conf.set("fs.defaultFS", "file:///");
        conf.set("mapreduce.framework.name", "local");

        Job job = Job.getInstance(conf, "arxiv-category-classification");
        job.setJarByClass(Main.class);
        job.setMapperClass(CategoryMapper.class);
        job.setReducerClass(CategoryReducer.class);
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);
        job.setInputFormatClass(TextInputFormat.class);
        job.setOutputFormatClass(TextOutputFormat.class);
        job.setNumReduceTasks(1);

        FileInputFormat.addInputPath(job, new Path(inputPath));
        FileOutputFormat.setOutputPath(job, new Path(outputPath));

        boolean success = job.waitForCompletion(true);

        if (success) {
            // 自动运行JsonFormatter生成最终JSON文件
            System.out.println("MapReduce任务完成，正在生成最终JSON文件...");
            JsonFormatter.main(new String[] {});
        }

        System.exit(success ? 0 : 1);
    }
}
