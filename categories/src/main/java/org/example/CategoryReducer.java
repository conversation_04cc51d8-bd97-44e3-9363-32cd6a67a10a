package org.example;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Hadoop Reducer类 - 聚合每个分类的论文ID列表
 */
public class CategoryReducer extends Reducer<Text, Text, Text, Text> {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Text result = new Text();
    
    @Override
    protected void reduce(Text key, Iterable<Text> values, Context context) 
            throws IOException, InterruptedException {
        
        try {
            String category = key.toString();
            List<String> paperIds = new ArrayList<>();
            
            // 收集该分类下的所有论文ID
            for (Text value : values) {
                paperIds.add(value.toString());
            }
            
            // 创建JSON对象
            ObjectNode categoryObject = objectMapper.createObjectNode();
            categoryObject.put("category", category);
            
            // 创建论文ID数组
            ArrayNode idsArray = objectMapper.createArrayNode();
            for (String paperId : paperIds) {
                idsArray.add(paperId);
            }
            categoryObject.set("id", idsArray);
            
            // 转换为JSON字符串
            String jsonOutput = objectMapper.writeValueAsString(categoryObject);
            result.set(jsonOutput);
            
            // 输出结果 (使用空键，这样所有结果会写入同一个文件)
            context.write(new Text(""), result);
            
            // 更新计数器
            context.getCounter("ARXIV_PROCESSING", "CATEGORIES_PROCESSED").increment(1);
            context.getCounter("ARXIV_PROCESSING", "TOTAL_PAPERS").increment(paperIds.size());
            
            // 输出处理进度
            context.setStatus("已处理分类: " + category + " (包含 " + paperIds.size() + " 篇论文)");
            
        } catch (Exception e) {
            System.err.println("Reducer处理分类时出错: " + key.toString());
            System.err.println("错误信息: " + e.getMessage());
            context.getCounter("ARXIV_PROCESSING", "REDUCE_ERRORS").increment(1);
        }
    }
}
