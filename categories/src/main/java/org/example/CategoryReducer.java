package org.example;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Hadoop Reducer类 - 聚合每个分类的论文ID列表
 */
public class CategoryReducer extends Reducer<Text, Text, Text, Text> {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Text result = new Text();

    @Override
    protected void reduce(Text key, Iterable<Text> values, Context context)
            throws IOException, InterruptedException {

        try {
            String category = key.toString();
            List<String> paperIds = new ArrayList<>();

            for (Text value : values) {
                paperIds.add(value.toString());
            }

            ObjectNode categoryObject = objectMapper.createObjectNode();
            categoryObject.put("category", category);

            ArrayNode idsArray = objectMapper.createArrayNode();
            for (String paperId : paperIds) {
                idsArray.add(paperId);
            }
            categoryObject.set("id", idsArray);

            String jsonOutput = objectMapper.writeValueAsString(categoryObject);
            result.set(jsonOutput);

            context.write(new Text(""), result);

        } catch (Exception e) {
            // 忽略错误，继续处理
        }
    }
}
