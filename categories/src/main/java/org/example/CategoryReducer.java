package org.example;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Reducer class for aggregating paper IDs by category
 */
public class CategoryReducer extends Reducer<Text, Text, Text, Text> {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Text result = new Text();
    
    @Override
    protected void reduce(Text key, Iterable<Text> values, Context context) 
            throws IOException, InterruptedException {
        
        try {
            String category = key.toString();
            List<String> paperIds = new ArrayList<>();
            
            // Collect all paper IDs for this category
            for (Text value : values) {
                paperIds.add(value.toString());
            }
            
            // Create JSON object for this category
            ObjectNode categoryObject = objectMapper.createObjectNode();
            categoryObject.put("category", category);
            
            // Create array of paper IDs
            ArrayNode idsArray = objectMapper.createArrayNode();
            for (String paperId : paperIds) {
                idsArray.add(paperId);
            }
            categoryObject.set("id", idsArray);
            
            // Convert to JSON string
            String jsonOutput = objectMapper.writeValueAsString(categoryObject);
            result.set(jsonOutput);
            
            // Write the result
            context.write(new Text(""), result);
            
        } catch (Exception e) {
            System.err.println("Error in reducer for category: " + key.toString());
            System.err.println("Error: " + e.getMessage());
        }
    }
}
