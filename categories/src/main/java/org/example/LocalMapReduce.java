package org.example;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

/**
 * 本地模拟MapReduce实现 - 使用Hadoop MapReduce的思想但在本地运行
 */
public class LocalMapReduce {
    
    public static void main(String[] args) throws Exception {
        if (args.length != 2) {
            System.err.println("用法: java -cp target/classes org.example.LocalMapReduce <输入目录> <输出目录>");
            System.err.println("示例: java -cp target/classes org.example.LocalMapReduce ../expdata output");
            System.exit(-1);
        }
        
        String inputDir = args[0];
        String outputDir = args[1];
        
        System.out.println("=== 本地MapReduce ArXiv论文分类 ===");
        System.out.println("输入目录: " + inputDir);
        System.out.println("输出目录: " + outputDir);
        
        // 创建输出目录
        Files.createDirectories(Paths.get(outputDir));
        
        long startTime = System.currentTimeMillis();
        
        // Map阶段：处理所有输入文件
        System.out.println("\n=== Map阶段 ===");
        Map<String, List<String>> intermediateResults = mapPhase(inputDir);
        
        // Reduce阶段：聚合结果
        System.out.println("\n=== Reduce阶段 ===");
        List<JsonNode> finalResults = reducePhase(intermediateResults);
        
        // 输出结果
        System.out.println("\n=== 输出阶段 ===");
        writeResults(finalResults, outputDir);
        
        long endTime = System.currentTimeMillis();
        long duration = (endTime - startTime) / 1000;
        
        System.out.println("\n=== 任务完成 ===");
        System.out.println("执行时间: " + duration + " 秒");
        System.out.println("处理的分类数: " + finalResults.size());
        
        // 显示统计信息
        showStatistics(finalResults);
    }
    
    /**
     * Map阶段：读取所有文件并提取分类-ID映射
     */
    private static Map<String, List<String>> mapPhase(String inputDir) throws IOException {
        Map<String, List<String>> results = new HashMap<>();
        ObjectMapper objectMapper = new ObjectMapper();
        
        File inputDirectory = new File(inputDir);
        if (!inputDirectory.exists() || !inputDirectory.isDirectory()) {
            throw new IOException("输入目录不存在: " + inputDir);
        }
        
        File[] jsonlFiles = inputDirectory.listFiles((dir, name) -> name.endsWith(".jsonl"));
        if (jsonlFiles == null || jsonlFiles.length == 0) {
            throw new IOException("在目录中没有找到.jsonl文件: " + inputDir);
        }
        
        System.out.println("找到 " + jsonlFiles.length + " 个数据文件");
        
        int totalLines = 0;
        int processedLines = 0;
        int errorLines = 0;
        
        for (File file : jsonlFiles) {
            System.out.println("处理文件: " + file.getName());
            
            try (BufferedReader reader = Files.newBufferedReader(file.toPath())) {
                String line;
                while ((line = reader.readLine()) != null) {
                    totalLines++;
                    line = line.trim();
                    if (line.isEmpty()) continue;
                    
                    try {
                        // 模拟Mapper的map方法
                        JsonNode jsonNode = objectMapper.readTree(line);
                        JsonNode categoryNode = jsonNode.get("arxiv_primary_category");
                        JsonNode idNode = jsonNode.get("id");
                        
                        if (categoryNode != null && idNode != null) {
                            String category = categoryNode.asText();
                            String paperId = idNode.asText();
                            
                            if (!category.isEmpty() && !paperId.isEmpty()) {
                                // 模拟context.write(category, paperId)
                                results.computeIfAbsent(category, k -> new ArrayList<>()).add(paperId);
                                processedLines++;
                            }
                        }
                        
                        if (totalLines % 10000 == 0) {
                            System.out.println("  已处理 " + totalLines + " 行");
                        }
                        
                    } catch (Exception e) {
                        errorLines++;
                        if (errorLines <= 5) { // 只显示前5个错误
                            System.err.println("解析JSON行时出错: " + e.getMessage());
                        }
                    }
                }
            }
        }
        
        System.out.println("Map阶段完成:");
        System.out.println("  总行数: " + totalLines);
        System.out.println("  成功处理: " + processedLines);
        System.out.println("  错误行数: " + errorLines);
        System.out.println("  发现分类: " + results.size());
        
        return results;
    }
    
    /**
     * Reduce阶段：将Map结果转换为最终JSON格式
     */
    private static List<JsonNode> reducePhase(Map<String, List<String>> mapResults) {
        List<JsonNode> results = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        
        int totalPapers = 0;
        
        for (Map.Entry<String, List<String>> entry : mapResults.entrySet()) {
            String category = entry.getKey();
            List<String> paperIds = entry.getValue();
            
            // 模拟Reducer的reduce方法
            ObjectNode categoryObject = objectMapper.createObjectNode();
            categoryObject.put("category", category);
            
            ArrayNode idsArray = objectMapper.createArrayNode();
            for (String paperId : paperIds) {
                idsArray.add(paperId);
            }
            categoryObject.set("id", idsArray);
            
            results.add(categoryObject);
            totalPapers += paperIds.size();
        }
        
        // 按分类名称排序
        results.sort((a, b) -> {
            String categoryA = a.get("category").asText();
            String categoryB = b.get("category").asText();
            return categoryA.compareTo(categoryB);
        });
        
        System.out.println("Reduce阶段完成:");
        System.out.println("  处理分类数: " + results.size());
        System.out.println("  总论文数: " + totalPapers);
        
        return results;
    }
    
    /**
     * 输出结果到文件
     */
    private static void writeResults(List<JsonNode> results, String outputDir) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 写入MapReduce风格的输出文件
        String mapReduceOutput = outputDir + "/part-r-00000";
        try (PrintWriter writer = new PrintWriter(new FileWriter(mapReduceOutput))) {
            for (JsonNode result : results) {
                // 模拟Hadoop MapReduce的输出格式：key\tvalue
                writer.println("\t" + objectMapper.writeValueAsString(result));
            }
        }
        
        // 写入最终的JSON数组文件
        String jsonOutput = outputDir + "/categories_result.json";
        ArrayNode finalArray = objectMapper.createArrayNode();
        for (JsonNode result : results) {
            finalArray.add(result);
        }
        
        try (FileWriter writer = new FileWriter(jsonOutput)) {
            String prettyJson = objectMapper.writerWithDefaultPrettyPrinter()
                    .writeValueAsString(finalArray);
            writer.write(prettyJson);
        }
        
        System.out.println("输出文件:");
        System.out.println("  MapReduce格式: " + mapReduceOutput);
        System.out.println("  JSON数组格式: " + jsonOutput);
    }
    
    /**
     * 显示统计信息
     */
    private static void showStatistics(List<JsonNode> results) {
        System.out.println("\n=== 统计信息 ===");
        
        // 按论文数量排序
        List<CategoryStat> stats = new ArrayList<>();
        int totalPapers = 0;
        
        for (JsonNode result : results) {
            String category = result.get("category").asText();
            int paperCount = result.get("id").size();
            totalPapers += paperCount;
            stats.add(new CategoryStat(category, paperCount));
        }
        
        stats.sort((a, b) -> Integer.compare(b.paperCount, a.paperCount));
        
        System.out.println("论文数量最多的前10个分类:");
        for (int i = 0; i < Math.min(10, stats.size()); i++) {
            CategoryStat stat = stats.get(i);
            System.out.printf("%2d. %-25s: %d篇论文\n", 
                i + 1, stat.categoryName, stat.paperCount);
        }
        
        System.out.println("\n总体统计:");
        System.out.println("总分类数: " + results.size());
        System.out.println("总论文数: " + totalPapers);
        System.out.printf("平均每个分类论文数: %.2f\n", (double) totalPapers / results.size());
    }
    
    /**
     * 分类统计辅助类
     */
    private static class CategoryStat {
        String categoryName;
        int paperCount;
        
        CategoryStat(String categoryName, int paperCount) {
            this.categoryName = categoryName;
            this.paperCount = paperCount;
        }
    }
}
