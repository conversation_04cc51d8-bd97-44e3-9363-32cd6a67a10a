#!/bin/bash

# Hadoop MapReduce ArXiv论文分类运行脚本

echo "=== Hadoop MapReduce ArXiv论文分类 ==="

# 设置Hadoop环境
export HADOOP_HOME=/home/<USER>/Downloads/hadoop-3.4.1
export PATH=$HADOOP_HOME/bin:$PATH
export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop

# 设置路径
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INPUT_DIR="../expdata"
OUTPUT_DIR="hadoop_output"
FINAL_OUTPUT="categories_result.json"

# 清理之前的输出
echo "清理之前的输出..."
rm -rf "$OUTPUT_DIR"
rm -f "$FINAL_OUTPUT"

# 构建项目
echo "构建项目..."
cd "$PROJECT_DIR"
mvn clean package -q

if [ $? -ne 0 ]; then
    echo "构建失败!"
    exit 1
fi

echo "构建成功!"

# 检查输入数据
if [ ! -d "$INPUT_DIR" ]; then
    echo "错误: 输入目录不存在: $INPUT_DIR"
    exit 1
fi

echo "输入目录: $INPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

# 检查Hadoop状态
echo "检查Hadoop版本..."
hadoop version | head -1

# 运行Hadoop MapReduce任务
echo ""
echo "=== 运行Hadoop MapReduce任务 ==="
hadoop jar "target/categories-1.0-SNAPSHOT.jar" org.example.Main "$INPUT_DIR" "$OUTPUT_DIR"

if [ $? -ne 0 ]; then
    echo "MapReduce任务失败!"
    exit 1
fi

echo ""
echo "=== MapReduce任务完成 ==="

# 检查输出文件
MAPREDUCE_OUTPUT="$OUTPUT_DIR/part-r-00000"
if [ ! -f "$MAPREDUCE_OUTPUT" ]; then
    echo "错误: MapReduce输出文件不存在: $MAPREDUCE_OUTPUT"
    exit 1
fi

echo "MapReduce输出文件: $MAPREDUCE_OUTPUT"
echo "文件大小: $(du -h "$MAPREDUCE_OUTPUT" | cut -f1)"
echo "行数: $(wc -l < "$MAPREDUCE_OUTPUT")"

# 格式化输出为最终JSON
echo ""
echo "=== 格式化输出为JSON ==="
java -cp "target/categories-1.0-SNAPSHOT.jar:$HADOOP_HOME/share/hadoop/common/*:$HADOOP_HOME/share/hadoop/common/lib/*" org.example.JsonFormatter "$MAPREDUCE_OUTPUT" "$FINAL_OUTPUT"

if [ $? -ne 0 ]; then
    echo "JSON格式化失败!"
    exit 1
fi

echo ""
echo "=== 处理完成! ==="
echo "最终输出文件: $FINAL_OUTPUT"
echo "文件大小: $(du -h "$FINAL_OUTPUT" | cut -f1)"
echo ""

# 显示结果预览
echo "=== 结果预览 ==="
echo "前20行内容:"
head -20 "$FINAL_OUTPUT"
echo ""
echo "总行数: $(wc -l < "$FINAL_OUTPUT")"

# 验证JSON格式
echo ""
echo "=== 验证JSON格式 ==="
if command -v python3 &> /dev/null; then
    python3 -m json.tool "$FINAL_OUTPUT" > /dev/null && echo "✓ JSON格式正确" || echo "✗ JSON格式错误"
else
    echo "未安装Python3，跳过JSON格式验证"
fi

echo ""
echo "=== 任务完成! ==="
echo "Hadoop MapReduce输出: $OUTPUT_DIR/"
echo "最终JSON文件: $FINAL_OUTPUT"
