package org.example;

import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Reducer类 - 处理引用计数和DOI映射
 *
 * 处理三种类型的键：
 * 1. PAPER:title - 论文标题
 * 2. MAPPING:doi:title - DOI到标题的映射
 * 3. CITED:doi - 被引用的DOI
 */
public class CitationReducer extends Reducer<Text, IntWritable, Text, IntWritable> {

    private final IntWritable result = new IntWritable();
    private final Text outputKey = new Text();

    // 存储DOI到标题的映射
    private Map<String, String> doiToTitle = new HashMap<>();
    // 存储DOI的引用次数
    private Map<String, Integer> doiCitationCount = new HashMap<>();
    // 存储论文标题
    private Map<String, Integer> paperTitles = new HashMap<>();

    @Override
    protected void reduce(Text key, Iterable<IntWritable> values, Context context)
            throws IOException, InterruptedException {

        String keyStr = key.toString();

        if (keyStr.startsWith("PAPER:")) {
            // 处理论文标题
            String title = keyStr.substring(6); // 去掉"PAPER:"前缀
            paperTitles.put(title, 0); // 初始化为0

        } else if (keyStr.startsWith("MAPPING:")) {
            // 处理DOI到标题的映射
            String mapping = keyStr.substring(8); // 去掉"MAPPING:"前缀
            String[] parts = mapping.split(":", 2);
            if (parts.length == 2) {
                String doi = parts[0];
                String title = parts[1];
                doiToTitle.put(doi, title);
            }

        } else if (keyStr.startsWith("CITED:")) {
            // 处理被引用的DOI
            String doi = keyStr.substring(6); // 去掉"CITED:"前缀
            int sum = 0;
            for (IntWritable value : values) {
                sum += value.get();
            }
            doiCitationCount.put(doi, doiCitationCount.getOrDefault(doi, 0) + sum);
        }
    }

    @Override
    protected void cleanup(Context context) throws IOException, InterruptedException {
        // 在cleanup阶段输出最终结果

        // 首先，将DOI的引用次数映射到论文标题
        for (Map.Entry<String, Integer> entry : doiCitationCount.entrySet()) {
            String doi = entry.getKey();
            int citationCount = entry.getValue();

            if (doiToTitle.containsKey(doi)) {
                String title = doiToTitle.get(doi);
                if (paperTitles.containsKey(title)) {
                    paperTitles.put(title, paperTitles.get(title) + citationCount);
                }
            }
        }

        // 输出所有论文的引用计数
        for (Map.Entry<String, Integer> entry : paperTitles.entrySet()) {
            outputKey.set(entry.getKey());
            result.set(entry.getValue());
            context.write(outputKey, result);
        }
    }
}
