package org.example;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;

/**
 * Mapper类 - 提取论文信息和引用关系
 *
 * 输出三种类型的键值对：
 * 1. PAPER:title -> 0 (初始化论文)
 * 2. MAPPING:doi:title -> 0 (DOI到标题的映射)
 * 3. CITED:doi -> 1 (被引用的DOI)
 */
public class CitationMapper extends Mapper<LongWritable, Text, Text, IntWritable> {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Text outputKey = new Text();
    private final IntWritable one = new IntWritable(1);
    private final IntWritable zero = new IntWritable(0);

    @Override
    protected void map(LongWritable key, Text value, Context context) throws IOException, InterruptedException {
        String line = value.toString().trim();
        if (line.isEmpty()) {
            return;
        }

        try {
            JsonNode jsonNode = objectMapper.readTree(line);

            // 1. 提取当前论文的标题和DOI，建立映射关系
            JsonNode titleNode = jsonNode.get("title");
            JsonNode doiNode = jsonNode.get("doi");

            if (titleNode != null && !titleNode.asText().isEmpty()) {
                String title = titleNode.asText().trim();

                // 输出论文标题，初始化引用计数为0
                outputKey.set("PAPER:" + title);
                context.write(outputKey, zero);

                // 如果有DOI，建立DOI到标题的映射
                if (doiNode != null && !doiNode.asText().isEmpty()) {
                    String doi = doiNode.asText().trim();
                    outputKey.set("MAPPING:" + doi + ":" + title);
                    context.write(outputKey, zero);
                }
            }

            // 2. 处理引用信息 - 记录被引用的DOI
            JsonNode referencesNode = jsonNode.get("references");
            if (referencesNode != null && referencesNode.isArray()) {
                for (JsonNode reference : referencesNode) {
                    JsonNode refDoiNode = reference.get("DOI");
                    if (refDoiNode != null && !refDoiNode.asText().isEmpty()) {
                        String refDoi = refDoiNode.asText().trim();
                        outputKey.set("CITED:" + refDoi);
                        context.write(outputKey, one);
                    }
                }
            }

        } catch (Exception e) {
            // 忽略解析错误的行
        }
    }
}