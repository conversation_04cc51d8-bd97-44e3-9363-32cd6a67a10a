// src/main/java/org/example/CitationMapper.java
package org.example;

import java.io.IOException;
import java.util.*;
import org.apache.hadoop.io.*;
import org.apache.hadoop.mapreduce.Mapper;
import org.json.JSONObject;
import org.json.JSONArray;

public class CitationMapper extends Mapper<LongWritable, Text, Text, IntWritable> {
    private final static IntWritable one = new IntWritable(1);
    private Text paperTitle = new Text();

    @Override
    public void map(LongWritable key, Text value, Context context)
            throws IOException, InterruptedException {
        try {
            String line = value.toString();
            JSONObject paper = new JSONObject(line);

            // 获取引用文献列表
            if (paper.has("references")) {
                JSONArray references = paper.getJSONArray("references");
                for (int i = 0; i < references.length(); i++) {
                    String citedTitle = references.getString(i);
                    paperTitle.set(citedTitle);
                    context.write(paperTitle, one);
                }
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
    }
}