package org.example;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;

/**
 * Mapper类 - 提取论文标题和引用信息
 */
public class CitationMapper extends Mapper<LongWritable, Text, Text, IntWritable> {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Text titleKey = new Text();
    private final IntWritable one = new IntWritable(1);
    private final IntWritable zero = new IntWritable(0);

    @Override
    protected void map(LongWritable key, Text value, Context context) throws IOException, InterruptedException {
        String line = value.toString().trim();
        if (line.isEmpty()) {
            return;
        }

        try {
            JsonNode jsonNode = objectMapper.readTree(line);
            
            // 提取当前论文的标题
            JsonNode titleNode = jsonNode.get("title");
            if (titleNode != null && !titleNode.asText().isEmpty()) {
                String title = titleNode.asText().trim();
                titleKey.set(title);
                // 初始化每篇论文的引用计数为0
                context.write(titleKey, zero);
            }
            
            // 处理引用信息 - 统计被引用的论文
            JsonNode referencesNode = jsonNode.get("references");
            if (referencesNode != null && referencesNode.isArray()) {
                for (JsonNode reference : referencesNode) {
                    // 尝试从引用中提取标题信息
                    // 注意：ArXiv数据中的references主要包含DOI等信息，不包含标题
                    // 这里我们主要统计有引用记录的情况
                    JsonNode doiNode = reference.get("DOI");
                    if (doiNode != null && !doiNode.asText().isEmpty()) {
                        // 对于有DOI的引用，我们用DOI作为标识
                        String doi = doiNode.asText().trim();
                        titleKey.set("DOI:" + doi);
                        context.write(titleKey, one);
                    }
                }
            }
            
        } catch (Exception e) {
            // 忽略解析错误的行
        }
    }
}
