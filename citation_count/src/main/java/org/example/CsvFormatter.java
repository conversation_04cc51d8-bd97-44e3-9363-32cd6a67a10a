package org.example;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 将MapReduce输出转换为CSV格式
 */
public class CsvFormatter {

    public static void main(String[] args) throws Exception {
        String inputFile = "citation_output/part-r-00000";
        String outputFile = "citation_result.csv";

        List<CitationRecord> records = new ArrayList<>();

        try (BufferedReader reader = Files.newBufferedReader(Paths.get(inputFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    // MapReduce输出格式: title\tcount
                    String[] parts = line.split("\t");
                    if (parts.length == 2) {
                        String title = parts[0];
                        int count = Integer.parseInt(parts[1]);

                        // 所有输出都是论文标题和引用计数
                        records.add(new CitationRecord(title, count));
                    }
                }
            }
        }

        // 按引用次数降序排序
        records.sort((a, b) -> Integer.compare(b.citationCount, a.citationCount));

        // 写入CSV文件
        try (FileWriter writer = new FileWriter(outputFile)) {
            // 写入CSV头部
            writer.write("title,citation_count\n");

            // 写入数据
            for (CitationRecord record : records) {
                // 处理标题中的逗号和引号
                String escapedTitle = escapeCsvField(record.title);
                writer.write(escapedTitle + "," + record.citationCount + "\n");
            }
        }

        System.out.println("CSV转换完成: " + outputFile);
        System.out.println("总论文数: " + records.size());

        // 显示统计信息
        showStatistics(records);
    }

    /**
     * 转义CSV字段中的特殊字符
     */
    private static String escapeCsvField(String field) {
        if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
            // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        return field;
    }

    /**
     * 显示统计信息
     */
    private static void showStatistics(List<CitationRecord> records) {
        System.out.println("\n=== 引用统计信息 ===");

        int totalCitations = records.stream().mapToInt(r -> r.citationCount).sum();
        long citedPapers = records.stream().filter(r -> r.citationCount > 0).count();

        System.out.println("被引用次数最多的前10篇论文:");
        for (int i = 0; i < Math.min(10, records.size()); i++) {
            CitationRecord record = records.get(i);
            String shortTitle = record.title.length() > 60 ? record.title.substring(0, 60) + "..." : record.title;
            System.out.printf("%2d. %s: %d次引用\n",
                    i + 1, shortTitle, record.citationCount);
        }

        System.out.println("\n总体统计:");
        System.out.println("总论文数: " + records.size());
        System.out.println("被引用论文数: " + citedPapers);
        System.out.println("总引用次数: " + totalCitations);
        if (records.size() > 0) {
            System.out.printf("平均引用次数: %.2f\n", (double) totalCitations / records.size());
        }
    }

    /**
     * 引用记录辅助类
     */
    private static class CitationRecord {
        String title;
        int citationCount;

        CitationRecord(String title, int citationCount) {
            this.title = title;
            this.citationCount = citationCount;
        }
    }
}
